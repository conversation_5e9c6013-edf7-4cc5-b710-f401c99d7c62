'use client'

import React, { useState, useRef } from 'react'
import { useTranslation } from '@/hooks/use-translation'
import DocumentCarousel from '../carousel/DocumentCarousel'
import type { EmblaCarouselType } from 'embla-carousel'

// Document categories and subcategories structure
interface DocumentSubcategory {
  name: string;
  path: string;
  category: string;
}

interface DocumentCategory {
  id: string;
  subcategories: DocumentSubcategory[];
}

// Define document categories and their subcategories
const DOCUMENT_CATEGORIES: DocumentCategory[] = [
  {
    id: 'contract',
    subcategories: [
      { name: 'NDA', path: '/home-page-carousel/contract-NDA.png', category: 'contract' },
      { name: 'Employee', path: '/home-page-carousel/contract-employee.webp', category: 'contract' }
    ]
  },
  {
    id: 'financial',
    subcategories: [
      { name: 'Financial Report', path: '/home-page-carousel/financial-report.png', category: 'financial' },
      { name: 'Financial Statement', path: '/home-page-carousel/financial-statement.png', category: 'financial' }
    ]
  },
  {
    id: 'generic',
    subcategories: [
      { name: 'Invoice', path: '/home-page-carousel/generic-invoice.png', category: 'generic' },
      { name: 'Purchase Order', path: '/home-page-carousel/generic-PO.webp', category: 'generic' },
      { name: 'RFP', path: '/home-page-carousel/generic-RFP.jpg', category: 'generic' }
    ]
  },
  {
    id: 'handwritten',
    subcategories: [
      { name: 'Flowchart', path: '/home-page-carousel/handwriten-flowchart.webp', category: 'handwritten' },
      { name: 'Form', path: '/home-page-carousel/handwriten-form.png', category: 'handwritten' }
    ]
  },
  {
    id: 'insurance',
    subcategories: [
      { name: 'Claim Form', path: '/home-page-carousel/insurance-claimform.png', category: 'insurance' },
      { name: 'Policy', path: '/home-page-carousel/insurance.pnb.png', category: 'insurance' }
    ]
  },
  {
    id: 'manufacturing',
    subcategories: [
      { name: 'Specification', path: '/home-page-carousel/manufacturing-spec.png', category: 'manufacturing' }
    ]
  }
];

export default function DocumentCarouselSection() {
  const { t } = useTranslation('homepage')

  // Create a single long list of all documents from all categories
  const allDocuments = DOCUMENT_CATEGORIES.flatMap(category => category.subcategories)
  const [currentSlide, setCurrentSlide] = useState(0)
  const carouselApiRef = useRef<EmblaCarouselType | null>(null)
  const resetAutoplayRef = useRef<(() => void) | null>(null)

  return (
    <section className="py-8 sm:py-12 lg:py-16 bg-gradient-to-br from-purple-50 to-pink-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-6 sm:mb-8 lg:mb-12">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-3 sm:mb-4 lg:mb-6">{t('documentCarousel.title')}</h2>
          <p className="text-gray-600 max-w-3xl mx-auto text-sm sm:text-base lg:text-lg px-2">
            {t('documentCarousel.description')}
          </p>
        </div>

        {/* Document Carousel */}
        <DocumentCarousel
          documents={allDocuments}
          onSlideChange={setCurrentSlide}
          onApiReady={(api, resetAutoplay) => {
            carouselApiRef.current = api
            resetAutoplayRef.current = resetAutoplay
          }}
        />

        {/* Document Catalogue Section */}
        <div className="mt-6 sm:mt-8 lg:mt-10 max-w-6xl mx-auto px-2">
          <div className="catalogue-section">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2 sm:gap-3 lg:gap-4">
              {DOCUMENT_CATEGORIES.map((category) => (
                <div key={category.id} className="text-center">
                  <h4 className="text-xs sm:text-sm font-bold text-gray-800 mb-1 sm:mb-2 capitalize tracking-wide">
                    {t(`carousel.categories.${category.id}`)}
                  </h4>
                  <div className="space-y-1 sm:space-y-2">
                    {category.subcategories.map((subcat) => {
                      // Calculate the global index for this document
                      const globalIndex = allDocuments.findIndex(doc => doc.path === subcat.path)
                      const isActive = currentSlide === globalIndex

                      return (
                        <button
                          key={subcat.path}
                          onClick={() => {
                            if (carouselApiRef.current) {
                              carouselApiRef.current.scrollTo(globalIndex)
                              // Reset autoplay timer when user manually navigates
                              if (resetAutoplayRef.current) {
                                resetAutoplayRef.current()
                              }
                            }
                          }}
                          className={`catalogue-button w-full px-1 sm:px-2 py-1 sm:py-2 rounded text-xs sm:text-sm font-medium transition-all duration-300 touch-manipulation ${
                            isActive
                              ? 'bg-gradient-to-r from-purple-600 via-blue-600 to-pink-600 text-white shadow-sm'
                              : 'bg-white text-gray-700 hover:bg-gray-50 shadow-sm hover:shadow-md active:bg-gray-100'
                          }`}
                        >
                          {t(`documentNames.${subcat.name}`)}
                        </button>
                      )
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
