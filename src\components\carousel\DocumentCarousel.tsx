import React, { useCallback, useEffect, useRef } from 'react'
import {
  EmblaCarouselType,
  EmblaEventType,
  EmblaOptionsType
} from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import {
  NextButton,
  PrevButton,
  usePrevNextButtons
} from './EmblaCarouselArrowButtons'
import { DotButton, useDotButton } from './EmblaCarouselDotButton'
import { useTranslation } from '@/hooks/use-translation'

const TWEEN_FACTOR_BASE = 0.52

const numberWithinRange = (number: number, min: number, max: number): number =>
  Math.min(Math.max(number, min), max)

type DocumentType = {
  path: string
  name: string
  category: string
}

type PropType = {
  documents: DocumentType[]
  options?: EmblaOptionsType
  onSlideChange?: (index: number) => void
  onApiReady?: (api: EmblaCarouselType, resetAutoplay: () => void) => void
}

const DocumentCarousel: React.FC<PropType> = (props) => {
  const { documents, options, onSlideChange, onApiReady } = props
  const { t } = useTranslation('homepage')
  const autoplayIntervalRef = useRef<NodeJS.Timeout | null>(null)

  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: 'center',
      containScroll: 'trimSnaps',
      ...options
    }
  )
  const tweenFactor = useRef(0)

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi)

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick
  } = usePrevNextButtons(emblaApi)

  // Autoplay functionality
  const startAutoplay = useCallback(() => {
    if (autoplayIntervalRef.current) {
      clearInterval(autoplayIntervalRef.current)
    }
    autoplayIntervalRef.current = setInterval(() => {
      if (emblaApi) {
        emblaApi.scrollNext()
      }
    }, 3000)
  }, [emblaApi])

  const resetAutoplay = useCallback(() => {
    if (autoplayIntervalRef.current) {
      clearInterval(autoplayIntervalRef.current)
    }
    startAutoplay()
  }, [startAutoplay])

  // Start autoplay when emblaApi is ready
  useEffect(() => {
    if (emblaApi) {
      startAutoplay()
      return () => {
        if (autoplayIntervalRef.current) {
          clearInterval(autoplayIntervalRef.current)
        }
      }
    }
  }, [emblaApi, startAutoplay])

  // Notify parent component when slide changes and provide API
  useEffect(() => {
    if (emblaApi) {
      if (onApiReady) {
        onApiReady(emblaApi, resetAutoplay)
      }
      if (onSlideChange) {
        const onSelect = () => {
          onSlideChange(emblaApi.selectedScrollSnap())
        }
        emblaApi.on('select', onSelect)
        onSelect() // Call once to set initial state
        return () => {
          emblaApi.off('select', onSelect)
        }
      }
    }
  }, [emblaApi, onSlideChange, onApiReady, resetAutoplay])

  const [tweenValues, setTweenValues] = React.useState<Array<{ scale: number; opacity: number }>>([])

  const setTweenFactor = useCallback((emblaApi: EmblaCarouselType) => {
    tweenFactor.current = TWEEN_FACTOR_BASE * emblaApi.scrollSnapList().length
  }, [])

  const tweenScale = useCallback(
    (emblaApi: EmblaCarouselType, eventName?: EmblaEventType) => {
      const engine = emblaApi.internalEngine()
      const scrollProgress = emblaApi.scrollProgress()
      const slidesInView = emblaApi.slidesInView()
      const isScrollEvent = eventName === 'scroll'

      const newTweenValues = emblaApi.scrollSnapList().map((scrollSnap, snapIndex) => {
        let diffToTarget = scrollSnap - scrollProgress
        const slidesInSnap = engine.slideRegistry[snapIndex]

        slidesInSnap.forEach((slideIndex) => {
          if (isScrollEvent && !slidesInView.includes(slideIndex)) return

          if (engine.options.loop) {
            engine.slideLooper.loopPoints.forEach((loopItem) => {
              const target = loopItem.target()

              if (slideIndex === loopItem.index && target !== 0) {
                const sign = Math.sign(target)

                if (sign === -1) {
                  diffToTarget = scrollSnap - (1 + scrollProgress)
                }
                if (sign === 1) {
                  diffToTarget = scrollSnap + (1 - scrollProgress)
                }
              }
            })
          }

          const tweenValue = 1 - Math.abs(diffToTarget * tweenFactor.current)
          const scale = numberWithinRange(tweenValue, 0.8, 1.05)
          const opacity = numberWithinRange(tweenValue, 0.35, 1)

          return { scale, opacity }
        })

        const tweenValue = 1 - Math.abs(diffToTarget * tweenFactor.current)
        const scale = numberWithinRange(tweenValue, 0.8, 1.05)
        const opacity = numberWithinRange(tweenValue, 0.35, 1)

        return { scale, opacity }
      })

      setTweenValues(newTweenValues)
    },
    []
  )

  useEffect(() => {
    if (!emblaApi) return

    setTweenFactor(emblaApi)
    tweenScale(emblaApi)
    emblaApi
      .on('reInit', setTweenFactor)
      .on('reInit', tweenScale)
      .on('scroll', tweenScale)
      .on('slideFocus', tweenScale)
  }, [emblaApi, tweenScale])



  return (
    <div className="max-w-6xl mx-auto">
      {/* Carousel Container with External Navigation */}
      <div className="flex items-center gap-4">
        {/* Left Arrow */}
        <div className="hidden md:block">
          <div
            onClick={onPrevButtonClick}
            className={`w-16 h-32 bg-transparent hover:bg-gray-200 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300 ${
              prevBtnDisabled ? 'opacity-30 cursor-not-allowed' : ''
            }`}
          >
            <PrevButton onClick={() => {}} disabled={prevBtnDisabled} />
          </div>
        </div>

        {/* Carousel */}
        <div className="flex-1 overflow-hidden" ref={emblaRef}>
          <div className="flex">
            {documents.map((doc, index) => (
              <div
                className="flex-[0_0_85%] sm:flex-[0_0_75%] md:flex-[0_0_50%] lg:flex-[0_0_40%] min-w-0 pl-2"
                key={index}
                style={{
                  transform: `scale(${tweenValues[index]?.scale || 1})`,
                  opacity: tweenValues[index]?.opacity || 1,
                }}
              >
                <div className="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200 mx-1">
                  <div className="aspect-[4/5] p-2">
                    <img
                      src={doc.path}
                      alt={doc.name}
                      className="w-full h-full object-contain"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Right Arrow */}
        <div className="hidden md:block">
          <div
            onClick={onNextButtonClick}
            className={`w-16 h-32 bg-transparent hover:bg-gray-200 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300 ${
              nextBtnDisabled ? 'opacity-30 cursor-not-allowed' : ''
            }`}
          >
            <NextButton onClick={() => {}} disabled={nextBtnDisabled} />
          </div>
        </div>
      </div>

      {/* Dots Navigation */}
      <div className="flex justify-center mt-4 gap-2 px-4">
        {scrollSnaps.map((_, index) => (
          <DotButton
            key={index}
            onClick={() => onDotButtonClick(index)}
            className={`touch-manipulation ${
              index === selectedIndex
                ? 'bg-gradient-to-r from-purple-600 via-blue-600 to-pink-600'
                : 'bg-gray-300 hover:bg-gray-400 active:bg-gray-500'
            }`}
          />
        ))}
      </div>

      {/* Mobile Navigation Arrows */}
      <div className="flex justify-center mt-4 gap-4 md:hidden">
        <div
          onClick={prevBtnDisabled ? undefined : onPrevButtonClick}
          className={`p-3 rounded-full bg-white shadow-lg border border-gray-200 transition-all duration-300 ${
            prevBtnDisabled
              ? 'opacity-30 cursor-not-allowed'
              : 'hover:bg-gray-50 active:bg-gray-100 active:scale-95 cursor-pointer'
          }`}
        >
          <PrevButton onClick={() => {}} disabled={prevBtnDisabled} />
        </div>
        <div
          onClick={nextBtnDisabled ? undefined : onNextButtonClick}
          className={`p-3 rounded-full bg-white shadow-lg border border-gray-200 transition-all duration-300 ${
            nextBtnDisabled
              ? 'opacity-30 cursor-not-allowed'
              : 'hover:bg-gray-50 active:bg-gray-100 active:scale-95 cursor-pointer'
          }`}
        >
          <NextButton onClick={() => {}} disabled={nextBtnDisabled} />
        </div>
      </div>

      {/* Mobile arrows */}
      <div className="flex justify-center gap-4 mt-4 md:hidden">
        <PrevButton onClick={onPrevButtonClick} disabled={prevBtnDisabled} />
        <NextButton onClick={onNextButtonClick} disabled={nextBtnDisabled} />
      </div>
    </div>
  )
}

export default DocumentCarousel
