'use client'

import React, { useState, useEffect, useRef } from 'react'
import { useTranslation } from '@/hooks/use-translation'

interface FeatureStep {
  id: number
  title: string
  description: string
  image?: string
  video?: {
    resultPreview: string
    docxPreview: string
  }
  imageAlt: string
}

export function ScrollFeaturesSection() {
  const { t } = useTranslation('homepage')
  const [activeStep, setActiveStep] = useState(0)
  const [showResultPreview, setShowResultPreview] = useState(true)
  const [enlargedVideo, setEnlargedVideo] = useState<string | null>(null)
  const stepsRef = useRef<(HTMLDivElement | null)[]>([])
  const isUpdatingRef = useRef(false)

  // Define the five steps with your images and videos
  const steps: FeatureStep[] = [
    {
      id: 1,
      title: t('features.steps.step1.title'),
      description: t('features.steps.step1.description'),
      image: "/home-page-feature-section/select-template.png",
      imageAlt: "Select Template interface"
    },
    {
      id: 2,
      title: t('features.steps.step2.title'),
      description: t('features.steps.step2.description'),
      image: "/home-page-feature-section/select-section.png",
      imageAlt: "Select Section interface"
    },
    {
      id: 3,
      title: t('features.steps.step3.title'),
      description: t('features.steps.step3.description'),
      image: "/home-page-feature-section/select-language.png",
      imageAlt: "Select Language interface"
    },
    {
      id: 4,
      title: t('features.steps.step4.title'),
      description: t('features.steps.step4.description'),
      image: "/home-page-feature-section/upload-document.png",
      imageAlt: "Upload Document interface"
    },
    {
      id: 5,
      title: t('features.steps.step5.title'),
      description: t('features.steps.step5.description'),
      video: {
        resultPreview: "/home-page-feature-section/result-preview.mp4",
        docxPreview: "/home-page-feature-section/docx-preview.mp4"
      },
      imageAlt: "Analysis Results interface"
    }
  ]

  useEffect(() => {
    const handleScroll = () => {
      // Prevent scroll handling during layout updates
      if (isUpdatingRef.current) return

      const scrollY = window.scrollY
      const windowHeight = window.innerHeight
      const viewportCenter = scrollY + windowHeight / 2

      let closestIndex = 0
      let closestDistance = Infinity

      stepsRef.current.forEach((stepElement, index) => {
        if (!stepElement) return

        const rect = stepElement.getBoundingClientRect()
        const elementTop = rect.top + scrollY
        const elementCenter = elementTop + rect.height / 2
        const distance = Math.abs(elementCenter - viewportCenter)

        if (distance < closestDistance) {
          closestDistance = distance
          closestIndex = index
        }
      })

      // Only update if the active step actually changed
      setActiveStep(prevStep => {
        if (prevStep !== closestIndex) {
          isUpdatingRef.current = true
          // Reset the flag after a short delay to allow layout to settle
          setTimeout(() => {
            isUpdatingRef.current = false
          }, 100)
          return closestIndex
        }
        return prevStep
      })
    }

    // Debounce scroll events for better performance and stability
    let scrollTimeout: NodeJS.Timeout
    const debouncedScroll = () => {
      clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(() => {
        handleScroll()
      }, 50) // 50ms debounce
    }

    window.addEventListener('scroll', debouncedScroll, { passive: true })
    window.addEventListener('resize', debouncedScroll, { passive: true })

    // Initial check with delay to allow layout to settle
    setTimeout(handleScroll, 100)

    return () => {
      clearTimeout(scrollTimeout)
      window.removeEventListener('scroll', debouncedScroll)
      window.removeEventListener('resize', debouncedScroll)
    }
  }, [])

  return (
    <section className="py-12 sm:py-16 lg:py-20 bg-blue-50 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-8 sm:mb-12 lg:mb-16 animate-fade-in-up">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 sm:mb-6 px-2">
            {t('features.title')}
          </h2>
          <p className="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto px-4">
            {t('features.description')}
          </p>
        </div>

        <div className="space-y-8 sm:space-y-12 lg:space-y-16 xl:space-y-24">
          {steps.map((step, index) => (
            <div
              key={step.id}
              ref={(el) => {
                if (el) stepsRef.current[index] = el
              }}
              className="flex flex-col lg:flex-row gap-6 sm:gap-8 lg:gap-12 xl:gap-16 items-center"
            >
              {/* Video step uses regular layout but with larger video */}
              {step.video ? (
                <>
                  {/* Left side - Step Content */}
                  <div className={`w-full lg:w-1/2 order-2 lg:order-1 transition-all duration-700 ease-in-out ${
                    activeStep === index ? 'opacity-100 transform translate-x-0' : 'opacity-50 transform translate-x-2'
                  }`}>
                    <div className="flex items-start space-x-3 sm:space-x-4 lg:space-x-6 mb-4 sm:mb-6">
                      <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full flex items-center justify-center text-white font-bold text-base sm:text-lg lg:text-xl bg-gradient-to-r from-purple-600 to-pink-600 shadow-lg">
                        {step.id}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg sm:text-xl lg:text-2xl font-bold mb-2 sm:mb-3 lg:mb-4 text-gray-900">
                          {step.title}
                        </h3>
                      </div>
                    </div>

                    <p className="text-sm sm:text-base lg:text-lg leading-relaxed mb-4 sm:mb-6 text-gray-700">
                      {step.description}
                    </p>

                    {/* Features list for step 5 */}
                    <div className="space-y-2">
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          <span>Comprehensive summary</span>
                        </li>
                        <li className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          <span>Native output to Microsoft Word</span>
                        </li>
                        <li className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          <span>Export options</span>
                        </li>
                      </ul>
                    </div>
                  </div>

                  {/* Right side - Larger Video */}
                  <div className="w-full lg:w-1/2 order-1 lg:order-2">
                    <div className="relative">
                      {/* Switch for video step - positioned above video */}
                      {activeStep === index && (
                        <div className="flex justify-center mb-4">
                          <div className="flex bg-gray-100 rounded-xl p-1 shadow-sm border border-gray-200">
                            <button
                              onClick={() => {
                                isUpdatingRef.current = true
                                setShowResultPreview(true)
                                setTimeout(() => {
                                  isUpdatingRef.current = false
                                }, 200)
                              }}
                              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                                showResultPreview
                                  ? 'bg-white text-gray-900 shadow-sm'
                                  : 'text-gray-600 hover:text-gray-900'
                              }`}
                            >
                              {t('features.videoToggle.preview')}
                            </button>
                            <button
                              onClick={() => {
                                isUpdatingRef.current = true
                                setShowResultPreview(false)
                                setTimeout(() => {
                                  isUpdatingRef.current = false
                                }, 200)
                              }}
                              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${
                                !showResultPreview
                                  ? 'bg-white text-gray-900 shadow-sm'
                                  : 'text-gray-600 hover:text-gray-900'
                              }`}
                            >
                              <img src="/home-page-feature-section/docx_icon.png" alt="DOCX" className="w-4 h-4" />
                              <span>{t('features.videoToggle.docx')}</span>
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Larger video container - bigger than step 4 */}
                      <div className={`relative bg-white rounded-lg sm:rounded-xl lg:rounded-2xl shadow-lg sm:shadow-xl lg:shadow-2xl overflow-hidden border border-gray-200 transition-all duration-700 ease-in-out ${
                        activeStep === index
                          ? 'opacity-100 shadow-xl sm:shadow-2xl'
                          : 'opacity-30 shadow-md sm:shadow-lg'
                      }`}>
                        <div
                          className="aspect-[16/10] relative p-2 sm:p-4 lg:p-6 flex items-center justify-center cursor-pointer touch-manipulation"
                          onClick={() => {
                            const videoSrc = showResultPreview ? step.video?.resultPreview : step.video?.docxPreview
                            if (videoSrc) setEnlargedVideo(videoSrc)
                          }}
                        >
                          <video
                            key={showResultPreview ? 'result' : 'docx'}
                            src={showResultPreview ? step.video?.resultPreview : step.video?.docxPreview}
                            autoPlay
                            muted
                            loop
                            playsInline
                            className="w-full h-full object-contain hover:scale-105 transition-transform duration-200"
                          >
                            Your browser does not support the video tag.
                          </video>

                          {/* Simple click to enlarge indicator */}
                          <div
                            className="absolute top-8 right-8 bg-purple-600 text-white px-3 py-2 rounded-lg text-xs font-medium shadow-lg pointer-events-none"
                          >
                            <svg className="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                            </svg>
                            Click to enlarge
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                /* Regular layout for other steps */
                <>
              {/* Left side - Step Content */}
              <div className={`w-full lg:w-1/2 order-2 lg:order-1 transition-all duration-700 ease-in-out ${
                activeStep === index ? 'opacity-100 transform translate-x-0' : 'opacity-50 transform translate-x-2'
              }`}>
                <div className="flex items-start space-x-3 sm:space-x-4 lg:space-x-6 mb-4 sm:mb-6">
                  <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full flex items-center justify-center text-white font-bold text-base sm:text-lg lg:text-xl bg-gradient-to-r from-purple-600 to-pink-600 shadow-lg">
                    {step.id}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg sm:text-xl lg:text-2xl font-bold mb-2 sm:mb-3 lg:mb-4 text-gray-900">
                      {step.title}
                    </h3>
                  </div>
                </div>

                <p className="text-sm sm:text-base lg:text-lg leading-relaxed mb-4 sm:mb-6 text-gray-700">
                  {step.description}
                </p>

                {/* Additional features list */}
                <div className="space-y-2">
                  {index === 0 && (
                    <ul className="space-y-1 sm:space-y-2 text-xs sm:text-sm text-gray-600">
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Finance, Legal, Construction, Manufacturing and General templates</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Pre-configured analysis parameters</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Industry-specific insights</span>
                      </li>
                    </ul>
                  )}
                  {index === 1 && (
                    <ul className="space-y-1 sm:space-y-2 text-xs sm:text-sm text-gray-600">
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Intelligent section detection</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Custom section selection</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Focus on relevant content</span>
                      </li>
                    </ul>
                  )}
                  {index === 2 && (
                    <ul className="space-y-1 sm:space-y-2 text-xs sm:text-sm text-gray-600">
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>200+ Multiple language support</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Accurate translations by GenAI</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Localized output format</span>
                      </li>
                    </ul>
                  )}
                  {index === 3 && (
                    <ul className="space-y-1 sm:space-y-2 text-xs sm:text-sm text-gray-600">
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Secure large file upload</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Support multiple file formats</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                        <span>Up to 300 pages</span>
                      </li>
                    </ul>
                  )}

                </div>
              </div>

              {/* Right side - Media (only for non-video steps) */}
              <div className="w-full lg:w-1/2 order-1 lg:order-2">
                <div className={`relative bg-gray-50 rounded-lg sm:rounded-xl lg:rounded-2xl shadow-lg sm:shadow-xl lg:shadow-2xl overflow-hidden border border-gray-200 transition-all duration-700 ease-in-out ${
                  activeStep === index
                    ? 'opacity-100 shadow-xl sm:shadow-2xl'
                    : 'opacity-30 shadow-md sm:shadow-lg'
                }`}>
                  <div className="aspect-[4/3] relative p-2 sm:p-3 lg:p-4 flex items-center justify-center bg-gray-50">
                    <img
                      src={step.image}
                      alt={step.imageAlt}
                      className="max-w-full max-h-full object-contain"
                      style={{ objectPosition: 'center' }}
                    />
                  </div>
                </div>
              </div>
                </>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Video Enlargement Modal */}
      {enlargedVideo && (
        <div
          className="fixed inset-0 bg-black/90 backdrop-blur-md z-50 flex items-center justify-center p-2 sm:p-4 animate-in fade-in duration-300"
          onClick={() => setEnlargedVideo(null)}
        >
          <div className="relative max-w-7xl w-full max-h-[95vh] animate-in zoom-in-95 duration-300 ease-out">
            {/* Fancy close button */}
            <button
              onClick={() => setEnlargedVideo(null)}
              className="absolute top-2 right-2 sm:top-4 sm:right-4 z-10 bg-white/10 hover:bg-white/20 text-white rounded-full p-2 sm:p-3 transition-all duration-200 hover:scale-110 backdrop-blur-sm border border-white/20 touch-manipulation"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Video container with fancy border and shadow */}
            <div className="relative rounded-lg sm:rounded-2xl overflow-hidden shadow-2xl border border-white/20 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm">
              <video
                src={enlargedVideo}
                autoPlay
                muted
                loop
                playsInline
                className="w-full h-full object-contain rounded-2xl"
                onClick={(e) => e.stopPropagation()}
              >
                Your browser does not support the video tag.
              </video>

              {/* Subtle glow effect */}
              <div className="absolute inset-0 rounded-2xl shadow-[0_0_50px_rgba(147,51,234,0.3)] pointer-events-none"></div>
            </div>
          </div>
        </div>
      )}
    </section>
  )
}
